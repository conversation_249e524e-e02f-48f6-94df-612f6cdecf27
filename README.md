# RealSense Python Examples

This directory contains two example scripts for working with Intel RealSense cameras to generate pointclouds.

## Prerequisites

- Intel RealSense camera (tested with D455)
- Python virtual environment with pyrealsense2, numpy, and opencv-python installed
- RealSense SDK 2.0 installed on the system

## Examples

### 1. Basic Pointcloud Example (`pointcloud_basic.py`)

A simple example that captures depth and color frames and generates a pointcloud.

**Usage:**
```bash
pipenv run python pointcloud_basic.py
```

**Features:**
- Real-time pointcloud generation
- Color and depth image display
- Point counting and filtering (removes points > 1m)
- Save pointcloud to PLY file (press 's')

**Controls:**
- `q` - quit
- `s` - save pointcloud to PLY file

### 2. Advanced Temporal Filtering Example (`pointcloud_temporal_filter.py`)

A more advanced example that demonstrates various depth post-processing filters including temporal filtering.

**Usage:**
```bash
# Enable temporal filtering only
pipenv run python pointcloud_temporal_filter.py --enable-temporal

# Enable all filters
pipenv run python pointcloud_temporal_filter.py --enable-temporal --enable-spatial --enable-decimation --enable-hole-filling

# Customize temporal filter parameters
pipenv run python pointcloud_temporal_filter.py --enable-temporal --temporal-smooth-alpha 0.6 --temporal-smooth-delta 50
```

**Command Line Options:**
- `--enable-temporal` - Enable temporal noise reduction filter
- `--enable-spatial` - Enable spatial edge-preserving smoothing
- `--enable-decimation` - Enable decimation filter (reduces data)
- `--enable-hole-filling` - Enable hole filling filter
- `--temporal-smooth-alpha` - Temporal filter smooth alpha (0.0-1.0, default: 0.4)
- `--temporal-smooth-delta` - Temporal filter smooth delta (default: 20)
- `--temporal-holes-fill` - Temporal filter holes fill mode (default: 1)

**Features:**
- Multiple depth post-processing filters
- Real-time performance statistics
- Advanced filtering pipeline with disparity transform
- Interactive controls for pausing, statistics reset, etc.

**Controls:**
- `q` - quit
- `s` - save pointcloud to PLY file
- `r` - reset statistics
- `f` - toggle filter info display
- `SPACE` - pause/resume

## Filter Explanations

### Temporal Filter
Reduces temporal noise by comparing consecutive frames. The temporal filter uses the following parameters:
- **Alpha (0.0-1.0)**: Controls the amount of temporal smoothing. Higher values = more smoothing
- **Delta**: Threshold for pixel difference detection
- **Holes Fill**: Method for filling holes in the temporal domain

### Spatial Filter
Applies edge-preserving spatial smoothing to reduce noise while maintaining sharp edges.

### Decimation Filter
Reduces the amount of data by sub-sampling the depth image, improving performance.

### Hole Filling Filter
Fills holes in the depth map using neighboring pixel values.

### Threshold Filter
Removes depth values outside a specified range (always enabled, 0.15m - 4.0m).

## Output Files

Both examples can save pointclouds to PLY files:
- Basic example: `pointcloud_XXXX.ply`
- Advanced example: `filtered_pointcloud_TIMESTAMP.ply`

PLY files can be opened with:
- CloudCompare
- MeshLab
- Open3D
- PCL Viewer

## Tips

1. **Temporal Filtering**: Works best when the camera and scene are relatively stable
2. **Performance**: Use `--enable-decimation` for better performance on slower systems
3. **Noise Reduction**: Combine temporal and spatial filters for best noise reduction
4. **Hole Filling**: Use hole filling as the last step in the filter pipeline

## Troubleshooting

1. **No device found**: Make sure your RealSense camera is connected and recognized
2. **Permission issues**: Check that udev rules are properly installed
3. **Poor performance**: Try enabling decimation filter or reducing filter complexity
4. **Temporal artifacts**: Adjust temporal filter alpha and delta parameters

## Example Usage Scenarios

```bash
# For static scenes with high quality
pipenv run python pointcloud_temporal_filter.py --enable-temporal --enable-spatial --enable-hole-filling --temporal-smooth-alpha 0.6

# For dynamic scenes with good performance
pipenv run python pointcloud_temporal_filter.py --enable-spatial --enable-decimation

# For real-time applications
pipenv run python pointcloud_basic.py
```
