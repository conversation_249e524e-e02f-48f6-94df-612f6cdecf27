# Open3D Window Closure and Escape Key Implementation

## Overview

This implementation adds graceful program termination capabilities to the RealSense point cloud visualization by detecting:
- ESC key presses
- Window closure events
- Keyboard interrupts (Ctrl+C)

## Implementation Details

### 1. Global State Management

```python
_should_stop = False  # Flag to indicate when to stop the program
```

A global flag is used to signal when the program should terminate, allowing communication between the key callback and the main visualization loop.

### 2. Key Callback Function

```python
def key_callback(vis, key, action):
    """Callback function for key events in the visualizer."""
    global _should_stop
    if key == 256:  # ESC key code
        logger.info("Escape key pressed, stopping program")
        _should_stop = True
        return True
    return False
```

This callback is triggered when keys are pressed in the Open3D window. The ESC key (code 256) sets the stop flag.

### 3. Visualizer Initialization

```python
# Register key callback for escape key detection
_visualizer.register_key_callback(256, key_callback)  # ESC key
```

The key callback is registered during visualizer initialization to capture ESC key events.

### 4. Window Closure Detection

```python
# Check if escape was pressed or window should be closed
global _should_stop
if _should_stop:
    return None  # Signal to stop the main loop
    
# Check if window was closed by user
try:
    if not _visualizer.poll_events():
        logger.info("Visualizer window closed, stopping program")
        return None  # Signal to stop the main loop
except Exception as e:
    # Window was closed externally
    logger.info(f"Visualizer window closed externally: {e}, stopping program")
    return None
```

The visualization loop checks both the stop flag and window state to detect termination conditions.

### 5. Main Loop Integration

```python
box_position = get_box_position(pcd, visualize=True)

# Check if visualizer window was closed or escape was pressed
if box_position is None:
    logger.info("Stopping program due to window closure or escape key")
    break
```

The main loop checks if `get_box_position()` returns `None`, which signals that the program should terminate.

### 6. Keyboard Interrupt Handling

```python
try:
    # Main loop
    while True:
        # ... processing ...
except KeyboardInterrupt:
    logger.info("Stopping program due to keyboard interrupt (Ctrl+C)")
finally:
    camera.stop_capture()
    cleanup_visualizer()
```

Ctrl+C interrupts are caught and handled gracefully with proper cleanup.

### 7. Cleanup Function Enhancement

```python
def cleanup_visualizer():
    """Clean up the global visualizer instance."""
    global _visualizer, _pcd_geometry, _obb_geometry, _should_stop
    if _visualizer is not None:
        _visualizer.destroy_window()
        _visualizer = None
        _pcd_geometry = None
        _obb_geometry = None
    _should_stop = False  # Reset the stop flag
```

The cleanup function now resets the stop flag to ensure clean state for potential future runs.

## Usage

### Running the Program

```bash
python3 box_position.py
```

### Stopping the Program

Users can stop the program in three ways:

1. **ESC Key**: Press ESC while the Open3D window has focus
2. **Window Closure**: Click the X button to close the Open3D window
3. **Keyboard Interrupt**: Press Ctrl+C in the terminal

### Log Messages

The program provides clear feedback about why it's stopping:

- `"Escape key pressed, stopping program"`
- `"Visualizer window closed, stopping program"`
- `"Visualizer window closed externally, stopping program"`
- `"Stopping program due to keyboard interrupt (Ctrl+C)"`

## Benefits

1. **Graceful Shutdown**: Prevents resource leaks by properly cleaning up camera and visualizer resources
2. **User-Friendly**: Multiple intuitive ways to stop the program
3. **Robust Error Handling**: Handles various window closure scenarios
4. **Clear Feedback**: Informative log messages explain termination reasons
5. **Resource Management**: Ensures proper cleanup of RealSense camera and Open3D visualizer

## Technical Notes

- The ESC key code (256) is specific to Open3D's key event system
- Window closure detection uses `poll_events()` return value and exception handling
- The implementation maintains backward compatibility with existing code
- Global state is properly managed and reset during cleanup

This implementation provides a professional, user-friendly way to terminate the point cloud visualization program while ensuring proper resource cleanup.
