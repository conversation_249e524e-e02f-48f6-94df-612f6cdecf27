import pyrealsense2 as rs
import numpy as np
import open3d as o3d
from loguru import logger
from typing import Optional, <PERSON><PERSON>
from enum import Enum


class RealSenseCamera:
    """
    RealSense camera class providing a simple interface for capturing 3D point clouds
    and color images.

    Valid depth stream profiles:
        1280x720 @ [5, 15, 30] FPS
        848x480 @ [5, 15, 30, 60, 90] FPS
        848x100 @ [100] FPS
        640x480 @ [5, 15, 30, 60, 90] FPS
        640x360 @ [5, 15, 30, 60, 90] FPS
        480x270 @ [5, 15, 30, 60, 90] FPS
        424x240 @ [5, 15, 30, 60, 90] FPS
        256x144 @ [90] FPS

    Valid color stream profiles:
        1280x800 @ [5, 10, 15, 30] FPS
        1280x720 @ [5, 10, 15, 30] FPS
        848x480 @ [5, 15, 30, 60] FPS
        640x480 @ [5, 15, 30, 60] FPS
        640x360 @ [5, 15, 30, 60, 90] FPS
        480x270 @ [5, 15, 30, 60, 90] FPS
        424x240 @ [5, 15, 30, 60, 90] FPS
    """

    class Resolution(Enum):
        """Supported resolutions for RealSense D455 camera."""

        RES_256x144 = (256, 144)  # Depth only, 90 FPS
        RES_424x240 = (424, 240)
        RES_480x270 = (480, 270)
        RES_640x360 = (640, 360)
        RES_640x480 = (640, 480)
        RES_848x100 = (848, 100)  # Depth only, 100 FPS
        RES_848x480 = (848, 480)
        RES_1280x720 = (1280, 720)
        RES_1280x800 = (1280, 800)  # Color only

    class FrameRate(Enum):
        """Supported frame rates for RealSense D455 camera."""

        FPS_5 = 5
        FPS_6 = 6
        FPS_10 = 10
        FPS_15 = 15
        FPS_30 = 30
        FPS_60 = 60
        FPS_90 = 90
        FPS_100 = 100

    def __init__(
        self,
        depth_resolution: Resolution = Resolution.RES_1280x720,
        color_resolution: Resolution = Resolution.RES_1280x720,
        frame_rate: FrameRate = FrameRate.FPS_30,
        enable_decimation: bool = True,
        decimation_magnitude: int = 2,
        enable_spatial_filter: bool = True,
        spatial_smooth_alpha: float = 0.5,
        spatial_smooth_delta: int = 20,
        spatial_iterations: int = 2,
        enable_temporal_filter: bool = True,
        temporal_smooth_alpha: float = 0.4,
        temporal_smooth_delta: int = 20,
        enable_hole_filling: bool = True,
        hole_filling_mode: int = 1,
        enable_depth_to_disparity: bool = True,
        enable_disparity_to_depth: bool = True,
        depth_clamp_min: float = 0.1,
        depth_clamp_max: float = 4.0,
        statistical_outlier_removal: bool = True,
        stat_nb_neighbors: int = 20,
        stat_std_ratio: float = 2.0,
        radius_outlier_removal: bool = False,
        radius_nb_points: int = 16,
        radius_search_radius: float = 0.05,
        voxel_downsample: bool = False,
        voxel_size: float = 0.01,
    ):
        """
        Initialize the RealSense camera with comprehensive filtering options.

        Args:
            depth_resolution: Depth stream resolution
            color_resolution: Color stream resolution
            frame_rate: Camera frame rate
            enable_decimation: Enable decimation filter to reduce resolution
            decimation_magnitude: Decimation filter magnitude (1-8)
            enable_spatial_filter: Enable spatial edge-preserving filter
            spatial_smooth_alpha: Spatial filter smoothing parameter (0-1)
            spatial_smooth_delta: Spatial filter edge-preservation parameter
            spatial_iterations: Number of spatial filter iterations
            enable_temporal_filter: Enable temporal filter
            temporal_smooth_alpha: Temporal filter smoothing parameter (0-1)
            temporal_smooth_delta: Temporal filter threshold parameter
            enable_hole_filling: Enable hole filling filter
            hole_filling_mode: Hole filling mode (0-2)
            enable_depth_to_disparity: Enable depth to disparity transform
            enable_disparity_to_depth: Enable disparity to depth transform
            depth_clamp_min: Minimum depth clamp distance (meters)
            depth_clamp_max: Maximum depth clamp distance (meters)
            statistical_outlier_removal: Enable statistical outlier removal
            stat_nb_neighbors: Number of neighbors for statistical filtering
            stat_std_ratio: Standard deviation ratio for statistical filtering
            radius_outlier_removal: Enable radius outlier removal
            radius_nb_points: Minimum points in radius for radius filtering
            radius_search_radius: Search radius for radius filtering
            voxel_downsample: Enable voxel downsampling
            voxel_size: Voxel size for downsampling
        """

        # Store configuration
        self.depth_resolution = depth_resolution
        self.color_resolution = color_resolution
        self.frame_rate = frame_rate
        self.depth_clamp_min = depth_clamp_min
        self.depth_clamp_max = depth_clamp_max

        # Point cloud processing options
        self.statistical_outlier_removal = statistical_outlier_removal
        self.stat_nb_neighbors = stat_nb_neighbors
        self.stat_std_ratio = stat_std_ratio
        self.radius_outlier_removal = radius_outlier_removal
        self.radius_nb_points = radius_nb_points
        self.radius_search_radius = radius_search_radius
        self.voxel_downsample = voxel_downsample
        self.voxel_size = voxel_size

        # Initialize RealSense pipeline and config
        self.pipeline = rs.pipeline()
        self.config = rs.config()
        self.align = None
        self.colorizer = rs.colorizer()

        # Configure streams
        self.config.enable_stream(
            rs.stream.depth,
            depth_resolution.value[0],
            depth_resolution.value[1],
            rs.format.z16,
            frame_rate.value,
        )

        self.config.enable_stream(
            rs.stream.color,
            color_resolution.value[0],
            color_resolution.value[1],
            rs.format.bgr8,
            frame_rate.value,
        )

        # Initialize filters
        self.filters = []

        # Decimation filter
        if enable_decimation:
            decimation = rs.decimation_filter()
            decimation.set_option(rs.option.filter_magnitude, decimation_magnitude)
            self.filters.append(decimation)
            logger.info(f"Decimation filter enabled with magnitude {decimation_magnitude}")

        # Depth to disparity transform
        if enable_depth_to_disparity:
            self.depth_to_disparity = rs.disparity_transform(True)
            self.filters.append(self.depth_to_disparity)
            logger.info("Depth to disparity transform enabled")

        # Spatial filter
        if enable_spatial_filter:
            spatial = rs.spatial_filter()
            spatial.set_option(rs.option.filter_smooth_alpha, spatial_smooth_alpha)
            spatial.set_option(rs.option.filter_smooth_delta, spatial_smooth_delta)
            spatial.set_option(rs.option.filter_magnitude, spatial_iterations)
            self.filters.append(spatial)
            logger.info(
                f"Spatial filter enabled (alpha={spatial_smooth_alpha}, delta={spatial_smooth_delta}, iterations={spatial_iterations})"
            )

        # Temporal filter
        if enable_temporal_filter:
            temporal = rs.temporal_filter()
            temporal.set_option(rs.option.filter_smooth_alpha, temporal_smooth_alpha)
            temporal.set_option(rs.option.filter_smooth_delta, temporal_smooth_delta)
            self.filters.append(temporal)
            logger.info(
                f"Temporal filter enabled (alpha={temporal_smooth_alpha}, delta={temporal_smooth_delta})"
            )

        # Disparity to depth transform
        if enable_disparity_to_depth:
            self.disparity_to_depth = rs.disparity_transform(False)
            self.filters.append(self.disparity_to_depth)
            logger.info("Disparity to depth transform enabled")

        # Hole filling filter
        if enable_hole_filling:
            hole_filling = rs.hole_filling_filter()
            hole_filling.set_option(rs.option.holes_fill, hole_filling_mode)
            self.filters.append(hole_filling)
            logger.info(f"Hole filling filter enabled (mode={hole_filling_mode})")

        # Pipeline profile for intrinsics
        self.profile = None
        self.depth_intrinsics = None
        self.color_intrinsics = None

        logger.info("RealSense camera initialized with comprehensive filtering")

    def start_capture(self) -> bool:
        """
        Start the RealSense camera capture.

        Returns:
            bool: True if capture started successfully, False otherwise
        """
        try:
            # Start streaming
            self.profile = self.pipeline.start(self.config)

            # Get intrinsics
            depth_profile = rs.video_stream_profile(self.profile.get_stream(rs.stream.depth))
            color_profile = rs.video_stream_profile(self.profile.get_stream(rs.stream.color))

            self.depth_intrinsics = depth_profile.get_intrinsics()
            self.color_intrinsics = color_profile.get_intrinsics()

            # Create align object to align color to depth
            self.align = rs.align(rs.stream.color)

            # Allow auto-exposure to settle
            for _ in range(30):
                self.pipeline.wait_for_frames()

            logger.info("RealSense camera capture started successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to start RealSense capture: {e}")
            return False

    def stop_capture(self) -> None:
        """Stop the RealSense camera capture."""
        try:
            self.pipeline.stop()
            logger.info("RealSense camera capture stopped")
        except Exception as e:
            logger.error(f"Error stopping RealSense capture: {e}")

    def get_frames(self) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """
        Get aligned color and depth frames.

        Returns:
            Tuple of (color_image, depth_image) or None if failed
        """
        try:
            # Wait for frames
            frames = self.pipeline.wait_for_frames()

            # Align frames
            assert self.align is not None
            aligned_frames = self.align.process(frames)

            # Get aligned frames
            color_frame = aligned_frames.get_color_frame()
            depth_frame = aligned_frames.get_depth_frame()

            if not color_frame or not depth_frame:
                return None

            # Apply filters to depth frame
            filtered_depth = depth_frame
            for filter_func in self.filters:
                filtered_depth = filter_func.process(filtered_depth)

            # Convert to numpy arrays
            color_image = np.asanyarray(color_frame.get_data())
            depth_image = np.asanyarray(filtered_depth.get_data())

            return color_image, depth_image

        except Exception as e:
            logger.error(f"Error getting frames: {e}")
            return None

    def get_pointcloud(self) -> Optional[o3d.geometry.PointCloud]:
        """
        Get a filtered point cloud from the RealSense camera.

        Returns:
            Open3D point cloud or None if failed
        """
        try:
            # Wait for frames
            frames = self.pipeline.wait_for_frames()

            # Align frames
            assert self.align is not None
            aligned_frames = self.align.process(frames)

            # Get aligned frames
            color_frame = aligned_frames.get_color_frame()
            depth_frame = aligned_frames.get_depth_frame()

            if not color_frame or not depth_frame:
                return None

            # Apply filters to depth frame
            filtered_depth = depth_frame
            for filter_func in self.filters:
                filtered_depth = filter_func.process(filtered_depth)

            # Create a new pointcloud object
            pc = rs.pointcloud()

            # Generate point cloud
            pc.map_to(color_frame)
            points = pc.calculate(filtered_depth)

            # Get vertices and texture coordinates
            vertices = np.asanyarray(points.get_vertices()).view(np.float32).reshape(-1, 3)
            colors = (
                np.asanyarray(points.get_texture_coordinates()).view(np.float32).reshape(-1, 2)
            )

            # Get color image
            color_image = np.asanyarray(color_frame.get_data())

            # Filter out invalid points (zero coordinates)
            valid_mask = ~np.all(vertices == 0, axis=1)
            vertices = vertices[valid_mask]
            colors = colors[valid_mask]

            # Apply depth clamping
            z_mask = (vertices[:, 2] >= self.depth_clamp_min) & (
                vertices[:, 2] <= self.depth_clamp_max
            )
            vertices = vertices[z_mask]
            colors = colors[z_mask]

            if len(vertices) == 0:
                logger.warning("No valid points after filtering")
                return None

            # Create Open3D point cloud
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(vertices)

            # Map texture coordinates to RGB colors
            h, w = color_image.shape[:2]

            # Ensure texture coordinates are valid
            colors = np.clip(colors, 0.0, 1.0)

            color_coords = np.column_stack(
                [
                    np.clip(colors[:, 0] * w, 0, w - 1).astype(int),
                    np.clip(colors[:, 1] * h, 0, h - 1).astype(int),
                ]
            )

            # Extract RGB values
            rgb_colors = color_image[color_coords[:, 1], color_coords[:, 0]]
            rgb_colors = rgb_colors.astype(np.float32) / 255.0

            # Convert BGR to RGB (RealSense uses BGR)
            rgb_colors = rgb_colors[:, [2, 1, 0]]

            pcd.colors = o3d.utility.Vector3dVector(rgb_colors)

            # Apply additional point cloud filters
            if self.statistical_outlier_removal:
                pcd, _ = pcd.remove_statistical_outlier(
                    nb_neighbors=self.stat_nb_neighbors, std_ratio=self.stat_std_ratio
                )

            if self.radius_outlier_removal:
                pcd, _ = pcd.remove_radius_outlier(
                    nb_points=self.radius_nb_points, radius=self.radius_search_radius
                )

            if self.voxel_downsample:
                pcd = pcd.voxel_down_sample(voxel_size=self.voxel_size)

            logger.trace(f"Point cloud generated with {len(pcd.points)} points")
            return pcd

        except Exception as e:
            logger.error(f"Error generating point cloud: {e}")
            return None

    def get_device_info(self) -> dict:
        """
        Get information about the connected RealSense device.

        Returns:
            Dictionary containing device information
        """
        try:
            if self.profile is None:
                return {"error": "Camera not started"}

            device = self.profile.get_device()
            info = {
                "name": device.get_info(rs.camera_info.name),
                "serial_number": device.get_info(rs.camera_info.serial_number),
                "firmware_version": device.get_info(rs.camera_info.firmware_version),
                "product_line": device.get_info(rs.camera_info.product_line),
                "depth_resolution": self.depth_resolution.value,
                "color_resolution": self.color_resolution.value,
                "frame_rate": self.frame_rate.value,
                "depth_clamp_range": (self.depth_clamp_min, self.depth_clamp_max),
                "filters_enabled": len(self.filters),
            }

            return info

        except Exception as e:
            logger.error(f"Error getting device info: {e}")
            return {"error": str(e)}

    def set_exposure(self, exposure_time: int) -> bool:
        """
        Set the exposure time for the color sensor.

        Args:
            exposure_time: Exposure time in microseconds

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.profile is None:
                logger.error("Camera not started")
                return False

            device = self.profile.get_device()
            color_sensor = device.first_color_sensor()

            # Disable auto exposure
            color_sensor.set_option(rs.option.enable_auto_exposure, 0)

            # Set exposure time
            color_sensor.set_option(rs.option.exposure, exposure_time)

            logger.info(f"Exposure time set to {exposure_time} microseconds")
            return True

        except Exception as e:
            logger.error(f"Error setting exposure: {e}")
            return False

    def enable_auto_exposure(self) -> bool:
        """
        Enable automatic exposure control.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.profile is None:
                logger.error("Camera not started")
                return False

            device = self.profile.get_device()
            color_sensor = device.first_color_sensor()

            # Enable auto exposure
            color_sensor.set_option(rs.option.enable_auto_exposure, 1)

            logger.info("Auto exposure enabled")
            return True

        except Exception as e:
            logger.error(f"Error enabling auto exposure: {e}")
            return False


if __name__ == "__main__":
    # Example usage
    camera = RealSenseCamera(
        depth_resolution=RealSenseCamera.Resolution.RES_640x480,
        color_resolution=RealSenseCamera.Resolution.RES_640x480,
        frame_rate=RealSenseCamera.FrameRate.FPS_30,
        enable_temporal_filter=True,
        enable_spatial_filter=True,
        statistical_outlier_removal=True,
    )

    if camera.start_capture():
        try:
            # Get device info
            info = camera.get_device_info()
            print(f"Connected to: {info}")

            # Capture a few point clouds
            for i in range(10):
                pcd = camera.get_pointcloud()
                if pcd is not None:
                    print(f"Frame {i + 1}: {len(pcd.points)} points")
                else:
                    print(f"Frame {i + 1}: Failed to get point cloud")

        finally:
            camera.stop_capture()
    else:
        print("Failed to start camera capture")
