import time
from dataclasses import dataclass
from pprint import pprint
from scipy.spatial.transform import Rotation as R
import numpy as np
import open3d as o3d
from loguru import logger

from camera_realsense import RealSenseCamera

# Global visualizer instance for reuse
_visualizer = None
_pcd_geometry = None
_obb_geometry = None


@dataclass
class BoxPosition:
    center: np.ndarray
    z_position: float
    extent: np.ndarray
    rotation: np.ndarray  # zxy
    volume: float


def get_box_position(pcd, visualize=False):
    # # ROI box (without conveyor)
    # pcd = pcd.crop(
    #     o3d.geometry.AxisAlignedBoundingBox(
    #         min_bound=(-330, -680, 400), max_bound=(250, 500, 1350)
    #     )
    # )

    points = np.asarray(pcd.points)

    # Get top (low Z value) of box by taking 10th percentile of Z values
    z_values = points[:, 2]
    try:
        top_box_z = float(np.percentile(z_values, 10))
    except IndexError:
        logger.error("Failed to compute top of box (10th percentile Z)")
        return None
    logger.trace(f"Top of box (10th percentile Z): {top_box_z:.3f} mm")

    # # Isolate top of box (50 mm margin around top_box_z)
    # pcd = pcd.crop(
    #     o3d.geometry.AxisAlignedBoundingBox(
    #         min_bound=(-np.inf, -np.inf, top_box_z - 50),
    #         max_bound=(np.inf, np.inf, top_box_z + 50),
    #     )
    # )
    # points = np.asarray(pcd.points)
    # logger.trace(
    #     f"Min and max Z after cropping: {points[:, 2].min():.3f} to {points[:, 2].max():.3f} mm"
    # )

    # # Remove single points (noise) but keep track of outliers
    # # cl, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=1.0)
    # cl, ind = pcd.remove_radius_outlier(nb_points=16, radius=20)
    # pcd = pcd.select_by_index(ind)

    # # Select the edges (less work for OBB in the next step, which makes it more stable)
    # cl2, ind2 = pcd.remove_radius_outlier(nb_points=16, radius=10)
    # pcd = pcd.select_by_index(ind2, invert=True)

    try:
        obb = pcd.get_minimal_oriented_bounding_box()
    except RuntimeError as e:
        logger.error(f"Failed to compute OBB: {e}")
        return None

    logger.trace(f"OBB center: {obb.center}")
    logger.trace(f"OBB extent: {obb.extent}")
    logger.trace(f"OBB rotation matrix: {obb.R}")
    logger.trace(f"OBB volume: {obb.volume()}")

    # We want to have the longest axis of the OBB be the length: the first axis.
    # The second axis should be the width, and the third axis should be the height.
    # The shortest axis of the OBB is the height.
    obb_extent = obb.extent.copy()
    obb_R = obb.R.copy()
    if obb_extent[0] < obb_extent[1]:
        obb_extent = np.array([obb_extent[1], obb_extent[0], obb_extent[2]])
        obb_R = obb_R @ np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    if obb_extent[1] < obb_extent[2]:
        obb_extent = np.array([obb_extent[0], obb_extent[2], obb_extent[1]])
        obb_R = obb_R @ np.array([[1, 0, 0], [0, 0, 1], [0, -1, 0]])
    if obb_extent[0] < obb_extent[1]:
        obb_extent = np.array([obb_extent[1], obb_extent[0], obb_extent[2]])
        obb_R = obb_R @ np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    logger.trace(f"OBB extent after sorting: {obb_extent}")

    # Get rotation angles from rotation matrix
    r = R.from_matrix(obb_R)
    angles = r.as_euler("zxy", degrees=True)
    logger.trace(
        f"z rotation: {angles[0]:.3f} deg, x rotation: {angles[1]:.3f} deg, y rotation: {angles[2]:.3f} deg"
    )

    if visualize:
        global _visualizer, _pcd_geometry, _obb_geometry

        # Initialize visualizer if not already created
        if _visualizer is None:
            _visualizer = o3d.visualization.Visualizer()  # type: ignore
            _visualizer.create_window(window_name="PLY Viewer")

            # Set rendering options for better visualization
            render_option = _visualizer.get_render_option()
            render_option.point_size = 2.0  # Make points more visible
            render_option.background_color = np.array([0.1, 0.1, 0.1])  # Dark background

            # Add initial geometries and store references
            _pcd_geometry = pcd
            _obb_geometry = obb
            _visualizer.add_geometry(_pcd_geometry)
            _visualizer.add_geometry(_obb_geometry)

            # # Set initial viewpoint after adding geometries
            # _visualizer.get_view_control().set_front([0.0, -1.0, -1.0])
            # _visualizer.get_view_control().set_lookat([0.0, 0.0, 800.0])
            # _visualizer.get_view_control().set_up([0.0, 0.0, 1.0])
            for
        else:
            # Update existing geometries in place
            _pcd_geometry.points = pcd.points
            _pcd_geometry.colors = pcd.colors
            if hasattr(pcd, "normals") and len(pcd.normals) > 0:
                _pcd_geometry.normals = pcd.normals

            # Update OBB geometry
            _obb_geometry.center = obb.center
            _obb_geometry.extent = obb.extent
            _obb_geometry.R = obb.R
            _obb_geometry.color = obb.color

            # Update the visualizer with the modified geometries
            _visualizer.update_geometry(_pcd_geometry)
            _visualizer.update_geometry(_obb_geometry)

        # Non-blocking update - this allows interaction
        _visualizer.poll_events()
        _visualizer.update_renderer()

    return BoxPosition(obb.center, top_box_z, obb_extent, angles, obb.volume())


def cleanup_visualizer():
    """Clean up the global visualizer instance."""
    global _visualizer, _pcd_geometry, _obb_geometry
    if _visualizer is not None:
        _visualizer.destroy_window()
        _visualizer = None
        _pcd_geometry = None
        _obb_geometry = None


if __name__ == "__main__":
    camera = RealSenseCamera()
    if not camera.start_capture():
        raise RuntimeError("Failed to start camera capture")

    try:
        while True:
            pcd = camera.get_pointcloud()
            assert pcd is not None

            # # ROI box (including conveyor)
            # pcd = pcd.crop(o3d.geometry.AxisAlignedBoundingBox(min_bound=(-330, -680, 400), max_bound=(250, 500, 2000)))

            box_position = get_box_position(pcd, visualize=True)
            pprint(box_position)
    finally:
        camera.stop_capture()
        cleanup_visualizer()
